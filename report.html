<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车漆漆膜检测报告</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f5f5;
            padding: 20px;
            line-height: 1.6;
        }
        
        .report-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .report-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .report-header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .report-header .report-id {
            font-size: 0.9em;
            opacity: 0.8;
        }
        
        .report-content {
            padding: 40px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            background: #f8f9ff;
        }
        
        .section h2 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .info-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e1e5e9;
        }
        
        .info-label {
            font-weight: 600;
            color: #555;
            font-size: 0.9em;
            margin-bottom: 5px;
        }
        
        .info-value {
            color: #333;
            font-size: 1.1em;
        }
        
        .status-indicator {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-normal {
            background: #d4edda;
            color: #155724;
        }
        
        .status-warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .thickness-chart {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e1e5e9;
            margin: 20px 0;
        }
        
        .chart-bar {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .chart-label {
            width: 100px;
            font-size: 0.9em;
            color: #666;
        }
        
        .chart-value {
            flex: 1;
            height: 25px;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 12px;
            position: relative;
            margin: 0 10px;
        }
        
        .chart-text {
            position: absolute;
            right: -50px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 0.9em;
            color: #333;
            font-weight: 600;
        }
        
        .issues-section {
            background: #fff3cd;
            border-left-color: #ffc107;
            border: 1px solid #ffeaa7;
        }
        
        .issues-section h2 {
            color: #856404;
        }
        
        .issues-list {
            list-style: none;
        }
        
        .issues-list li {
            background: white;
            padding: 10px 15px;
            margin-bottom: 8px;
            border-radius: 5px;
            border-left: 3px solid #ffc107;
            color: #856404;
        }
        
        .detected-values {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        
        .value-chip {
            background: #e3f2fd;
            color: #1976d2;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.9em;
            font-weight: 600;
        }
        
        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #666;
            font-size: 0.9em;
        }
        
        .print-btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            font-size: 1em;
            cursor: pointer;
            margin: 20px 0;
            transition: all 0.3s ease;
        }
        
        .print-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(79, 172, 254, 0.3);
        }
        
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .print-btn {
                display: none;
            }
            
            .report-container {
                box-shadow: none;
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="report-container">
        <div class="report-header">
            <h1>🚗 车漆漆膜检测报告</h1>
            <div class="report-id">报告编号: {{ report.report_id }}</div>
            <div class="report-id">生成时间: {{ report.generate_time }}</div>
        </div>
        
        <div class="report-content">
            <button class="print-btn" onclick="window.print()">🖨️ 打印报告</button>
            
            <!-- 基本信息 -->
            <div class="section">
                <h2>📋 基本信息</h2>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">小组编号</div>
                        <div class="info-value">{{ report.data.group_num }}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">检测部位</div>
                        <div class="info-value">{{ report.data.car_part }}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">检测时间</div>
                        <div class="info-value">{{ report.data.upload_time }}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">检测状态</div>
                        <div class="info-value">
                            {% if report.data.result_status == 'normal' %}
                                <span class="status-indicator status-normal">正常</span>
                            {% elif report.data.result_status in ['thin', 'thick', 'uneven'] %}
                                <span class="status-indicator status-warning">异常</span>
                            {% else %}
                                <span class="status-indicator status-error">错误</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 检测结果 -->
            <div class="section">
                <h2>📊 检测结果</h2>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">平均厚度</div>
                        <div class="info-value">{{ report.data.avg_thickness }} μm</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">最大厚度</div>
                        <div class="info-value">{{ report.data.max_thickness }} μm</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">最小厚度</div>
                        <div class="info-value">{{ report.data.min_thickness }} μm</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">厚度差异</div>
                        <div class="info-value">{{ report.data.thickness_diff }} μm</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">标准范围</div>
                        <div class="info-value">{{ report.data.standard_range }}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">检测点数</div>
                        <div class="info-value">{{ report.data.detection_count }} 个</div>
                    </div>
                </div>
                
                <!-- 厚度对比图表 -->
                <div class="thickness-chart">
                    <h3 style="margin-bottom: 15px; color: #333;">厚度对比</h3>
                    <div class="chart-bar">
                        <div class="chart-label">平均值</div>
                        <div class="chart-value" style="width: {{ (report.data.avg_thickness / 200 * 100) | round }}%;">
                            <div class="chart-text">{{ report.data.avg_thickness }}μm</div>
                        </div>
                    </div>
                    <div class="chart-bar">
                        <div class="chart-label">最大值</div>
                        <div class="chart-value" style="width: {{ (report.data.max_thickness / 200 * 100) | round }}%;">
                            <div class="chart-text">{{ report.data.max_thickness }}μm</div>
                        </div>
                    </div>
                    <div class="chart-bar">
                        <div class="chart-label">最小值</div>
                        <div class="chart-value" style="width: {{ (report.data.min_thickness / 200 * 100) | round }}%;">
                            <div class="chart-text">{{ report.data.min_thickness }}μm</div>
                        </div>
                    </div>
                </div>
                
                <!-- 检测到的数值 -->
                <div>
                    <h3 style="margin-bottom: 10px; color: #333;">检测到的厚度值</h3>
                    <div class="detected-values">
                        {% for value in report.data.detected_values %}
                            <span class="value-chip">{{ value }}μm</span>
                        {% endfor %}
                    </div>
                </div>
            </div>
            
            <!-- 问题分析 -->
            {% if report.data.issues %}
            <div class="section issues-section">
                <h2>⚠️ 问题分析</h2>
                <ul class="issues-list">
                    {% for issue in report.data.issues %}
                        <li>{{ issue }}</li>
                    {% endfor %}
                </ul>
            </div>
            {% endif %}
            
            <!-- 结论建议 -->
            <div class="section">
                <h2>💡 结论与建议</h2>
                <div style="background: white; padding: 20px; border-radius: 8px; border: 1px solid #e1e5e9;">
                    {% if report.data.result_status == 'normal' %}
                        <p style="color: #28a745; font-weight: 600;">✅ 检测结果正常</p>
                        <p style="margin-top: 10px; color: #666;">
                            该部位的漆膜厚度在标准范围内，厚度分布均匀，未发现明显的修补痕迹。
                        </p>
                    {% elif report.data.result_status == 'thin' %}
                        <p style="color: #ffc107; font-weight: 600;">⚠️ 漆膜厚度偏薄</p>
                        <p style="margin-top: 10px; color: #666;">
                            该部位的漆膜厚度低于标准范围，可能存在原厂漆面磨损或重新喷漆后厚度不足的情况。
                        </p>
                    {% elif report.data.result_status == 'thick' %}
                        <p style="color: #ffc107; font-weight: 600;">⚠️ 漆膜厚度偏厚</p>
                        <p style="margin-top: 10px; color: #666;">
                            该部位的漆膜厚度高于标准范围，可能存在重新喷漆或多次修补的情况。
                        </p>
                    {% elif report.data.result_status == 'uneven' %}
                        <p style="color: #ffc107; font-weight: 600;">⚠️ 漆膜厚度不均</p>
                        <p style="margin-top: 10px; color: #666;">
                            该部位的漆膜厚度差异较大，可能存在局部修补或喷漆工艺不均匀的情况。
                        </p>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>本报告由车漆漆膜检测系统自动生成</p>
            <p>检测数据仅供参考，具体情况请结合实际检查</p>
        </div>
    </div>
</body>
</html>
