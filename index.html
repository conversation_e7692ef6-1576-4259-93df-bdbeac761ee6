<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车漆漆膜检测系统 v2.0</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 1.1em;
        }
        
        .form-group select,
        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s ease;
        }
        
        .form-group select:focus,
        .form-group input:focus {
            outline: none;
            border-color: #4facfe;
        }
        
        .upload-area {
            border: 3px dashed #4facfe;
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            background: #f8f9ff;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: #00f2fe;
            background: #f0f8ff;
        }
        
        .upload-area.dragover {
            border-color: #00f2fe;
            background: #e6f3ff;
            transform: scale(1.02);
        }
        
        .upload-icon {
            font-size: 3em;
            color: #4facfe;
            margin-bottom: 15px;
        }
        
        .upload-text {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 10px;
        }
        
        .upload-hint {
            font-size: 0.9em;
            color: #999;
        }
        
        .file-input {
            display: none;
        }
        
        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(79, 172, 254, 0.3);
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 30px;
            background: linear-gradient(135deg, #f8f9ff 0%, #e6f3ff 100%);
            border-radius: 12px;
            border: 2px solid #4facfe;
            margin-top: 20px;
        }

        .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }

        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4facfe;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
        }

        .loading-dots {
            display: flex;
            gap: 5px;
            margin-top: 10px;
        }

        .loading-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4facfe;
            animation: bounce 1.4s ease-in-out infinite both;
        }

        .loading-dot:nth-child(1) { animation-delay: -0.32s; }
        .loading-dot:nth-child(2) { animation-delay: -0.16s; }
        .loading-dot:nth-child(3) { animation-delay: 0s; }

        .loading-text {
            font-size: 1.1em;
            color: #4facfe;
            font-weight: 600;
        }

        .loading-steps {
            font-size: 0.9em;
            color: #666;
            line-height: 1.6;
            text-align: left;
            max-width: 300px;
        }

        .loading-step {
            padding: 5px 0;
            opacity: 0.6;
            transition: opacity 0.3s ease;
        }

        .loading-step.active {
            opacity: 1;
            color: #4facfe;
            font-weight: 600;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes bounce {
            0%, 80%, 100% {
                transform: scale(0);
            } 40% {
                transform: scale(1);
            }
        }

        .result {
            display: none;
            margin-top: 30px;
            padding: 25px;
            border-radius: 12px;
            border-left: 5px solid #4facfe;
            background: #f8f9ff;
            animation: fadeInUp 0.5s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .result.success {
            border-left-color: #28a745;
            background: #f8fff9;
        }
        
        .result.warning {
            border-left-color: #ffc107;
            background: #fffdf8;
        }
        
        .result.error {
            border-left-color: #dc3545;
            background: #fff8f8;
        }
        
        .result h3 {
            margin-bottom: 15px;
            color: #333;
        }
        
        .result-item {
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .result-item:last-child {
            border-bottom: none;
        }
        
        .result-label {
            font-weight: 600;
            color: #555;
            display: inline-block;
            width: 120px;
        }
        
        .result-value {
            color: #333;
        }
        
        .issues {
            margin-top: 15px;
            padding: 15px;
            background: #fff3cd;
            border-radius: 8px;
            border-left: 4px solid #ffc107;
        }
        
        .issues h4 {
            color: #856404;
            margin-bottom: 10px;
        }
        
        .issues ul {
            margin-left: 20px;
        }
        
        .issues li {
            color: #856404;
            margin-bottom: 5px;
        }
        
        .preview-image {
            max-width: 100%;
            max-height: 300px;
            border-radius: 8px;
            margin-top: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .generate-report-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            margin-top: 15px;
        }
        
        .generate-report-btn:hover {
            box-shadow: 0 10px 20px rgba(40, 167, 69, 0.3);
        }

        /* 响应式设计优化 */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .container {
                margin: 0;
                border-radius: 10px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .content {
                padding: 20px;
            }

            .form-group label {
                font-size: 1em;
            }

            .upload-area {
                padding: 30px 20px;
            }

            .upload-icon {
                font-size: 2.5em;
            }

            .upload-text {
                font-size: 1.1em;
            }

            .btn {
                padding: 12px 20px;
                font-size: 1em;
            }

            .loading-steps {
                max-width: 250px;
                font-size: 0.8em;
            }

            .report-table {
                font-size: 0.8em;
            }

            .info-grid {
                grid-template-columns: 1fr;
                gap: 8px;
            }
        }

        @media (max-width: 480px) {
            .header h1 {
                font-size: 1.8em;
            }

            .header p {
                font-size: 1em;
            }

            .content {
                padding: 15px;
            }

            .upload-area {
                padding: 25px 15px;
            }

            .report-table th,
            .report-table td {
                padding: 6px 4px;
                font-size: 0.7em;
            }
        }

        /* 无障碍访问性改进 */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* 焦点样式改进 */
        .form-group select:focus,
        .form-group input:focus,
        .btn:focus {
            outline: 2px solid #4facfe;
            outline-offset: 2px;
        }

        /* 错误状态样式 */
        .form-group.error select,
        .form-group.error input {
            border-color: #dc3545;
            background-color: #fff5f5;
        }

        .error-message {
            color: #dc3545;
            font-size: 0.9em;
            margin-top: 5px;
            display: none;
        }

        .form-group.error .error-message {
            display: block;
        }

        /* 多部位上传样式 */
        .upload-sections {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .part-upload-section {
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            padding: 15px;
            background: #f8f9ff;
            transition: all 0.3s ease;
        }

        .part-upload-section.completed {
            border-color: #28a745;
            background: #f8fff9;
        }

        .part-upload-section.error {
            border-color: #dc3545;
            background: #fff8f8;
        }

        .part-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .part-status {
            font-size: 0.9em;
            padding: 3px 8px;
            border-radius: 12px;
            font-weight: 500;
        }

        .part-status.pending {
            background: #fff3cd;
            color: #856404;
        }

        .part-status.completed {
            background: #d4edda;
            color: #155724;
        }

        .part-upload-area {
            border: 2px dashed #4facfe;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            background: white;
            transition: all 0.3s ease;
            cursor: pointer;
            min-height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .part-upload-area:hover {
            border-color: #00f2fe;
            background: #f0f8ff;
        }

        .part-upload-area.dragover {
            border-color: #00f2fe;
            background: #e6f3ff;
            transform: scale(1.02);
        }

        .part-upload-area.completed {
            border-color: #28a745;
            background: #f8fff9;
        }

        .part-upload-icon {
            font-size: 2em;
            color: #4facfe;
            margin-bottom: 8px;
        }

        .part-upload-text {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 5px;
        }

        .part-upload-hint {
            font-size: 0.8em;
            color: #999;
        }

        .part-file-input {
            display: none;
        }

        .part-preview {
            margin-top: 10px;
            display: none;
        }

        .part-preview.show {
            display: block;
        }

        .part-preview-image {
            max-width: 100%;
            max-height: 100px;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .part-file-info {
            margin-top: 8px;
            font-size: 0.8em;
            color: #666;
        }

        .part-remove-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            cursor: pointer;
            margin-top: 5px;
            transition: background 0.3s ease;
        }

        .part-remove-btn:hover {
            background: #c82333;
        }

        /* 上传进度条 */
        .upload-progress {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9ff;
            border-radius: 8px;
            border-left: 4px solid #4facfe;
        }

        .progress-text {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e1e5e9;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .upload-sections {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .part-upload-area {
                padding: 15px;
                min-height: 100px;
            }

            .part-upload-icon {
                font-size: 1.5em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚗 车漆漆膜检测系统</h1>
            <p>智能识别车漆厚度，精准检测修补痕迹</p>
        </div>
        
        <div class="content">
            <form id="uploadForm">
                <div class="form-group">
                    <label for="groupNum">小组编号：</label>
                    <select id="groupNum" name="group_num" required aria-describedby="groupNum-error">
                        <option value="">请选择小组编号</option>
                        <option value="第1组">第1组</option>
                        <option value="第2组">第2组</option>
                        <option value="第3组">第3组</option>
                        <option value="第4组">第4组</option>
                        <option value="第5组">第5组</option>
                        <option value="第6组">第6组</option>
                        <option value="第7组">第7组</option>
                        <option value="第8组">第8组</option>
                        <option value="第9组">第9组</option>
                        <option value="第10组">第10组</option>
                    </select>
                    <div class="error-message" id="groupNum-error" role="alert">请选择小组编号</div>
                </div>
                
                <div class="form-group">
                    <label>车辆部位检测图片上传：</label>
                    <div class="upload-sections" id="uploadSections">
                        <!-- 动态生成各部位上传区域 -->
                    </div>
                    <div class="upload-progress">
                        <div class="progress-text">上传进度：<span id="uploadProgress">0/8</span></div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                        </div>
                    </div>
                    <div class="error-message" id="upload-error" role="alert" style="display: none;">请为所有车辆部位上传检测图片</div>
                </div>
                
                <button type="submit" class="btn" id="submitBtn">
                    🔍 开始检测
                </button>
            </form>
            
            <div class="loading" id="loading">
                <div class="loading-container">
                    <div class="loading-spinner"></div>
                    <div class="loading-text">🔍 正在智能分析图片</div>
                    <div class="loading-dots">
                        <div class="loading-dot"></div>
                        <div class="loading-dot"></div>
                        <div class="loading-dot"></div>
                    </div>
                    <div class="loading-steps" id="loadingSteps">
                        <div class="loading-step active" id="step1">📷 正在读取图片数据...</div>
                        <div class="loading-step" id="step2">🤖 启动OCR识别引擎...</div>
                        <div class="loading-step" id="step3">🔢 识别厚度数值...</div>
                        <div class="loading-step" id="step4">📊 分析检测结果...</div>
                        <div class="loading-step" id="step5">📋 生成评估报告...</div>
                    </div>
                </div>
            </div>
            
            <div class="result" id="result">
                <h3 id="resultTitle">检测结果</h3>
                <div id="resultContent"></div>
                <button type="button" class="btn generate-report-btn" id="generateReportBtn" style="display: none;">
                    📄 生成检测报告
                </button>
            </div>
        </div>
    </div>

    <script>
        // 🚀 车漆检测系统 v2.1 - 优化版本
        console.log('🚀 车漆检测系统 v2.1 - 优化版本已加载');
        console.log('⚡ 增强了表单验证、错误处理和无障碍访问性');

        // DOM元素获取
        const elements = {
            uploadForm: document.getElementById('uploadForm'),
            loading: document.getElementById('loading'),
            result: document.getElementById('result'),
            submitBtn: document.getElementById('submitBtn'),
            generateReportBtn: document.getElementById('generateReportBtn'),
            groupNum: document.getElementById('groupNum'),
            uploadSections: document.getElementById('uploadSections'),
            uploadProgress: document.getElementById('uploadProgress'),
            progressFill: document.getElementById('progressFill'),
            uploadError: document.getElementById('upload-error')
        };

        // 车辆部位配置
        const carParts = [
            { id: 'front-bumper', name: '前保险杠', icon: '🚗' },
            { id: 'rear-bumper', name: '后保险杠', icon: '🚙' },
            { id: 'front-fender', name: '前翼子板', icon: '🚘' },
            { id: 'rear-fender', name: '后翼子板', icon: '🚖' },
            { id: 'door', name: '车门', icon: '🚪' },
            { id: 'hood', name: '引擎盖', icon: '🔧' },
            { id: 'roof', name: '车顶', icon: '🏠' },
            { id: 'trunk', name: '后备箱盖', icon: '📦' }
        ];

        // 存储上传的文件
        const uploadedFiles = new Map();

        // 验证DOM元素是否正确获取
        Object.entries(elements).forEach(([key, element]) => {
            if (!element) {
                console.error(`DOM元素 ${key} 未找到`);
            }
        });

        let currentResult = null;

        // 初始化上传区域
        function initializeUploadSections() {
            const container = elements.uploadSections;
            container.innerHTML = '';

            carParts.forEach(part => {
                const section = createPartUploadSection(part);
                container.appendChild(section);
            });

            updateProgress();
        }

        // 创建单个部位上传区域
        function createPartUploadSection(part) {
            const section = document.createElement('div');
            section.className = 'part-upload-section';
            section.id = `section-${part.id}`;

            section.innerHTML = `
                <div class="part-title">
                    <span>${part.icon} ${part.name}</span>
                    <span class="part-status pending" id="status-${part.id}">待上传</span>
                </div>
                <div class="part-upload-area" id="upload-${part.id}" role="button" tabindex="0">
                    <div class="part-upload-icon" aria-hidden="true">📷</div>
                    <div class="part-upload-text">点击或拖拽图片到此处</div>
                    <div class="part-upload-hint">支持 JPG、PNG、GIF 格式，最大 16MB</div>
                    <input type="file" class="part-file-input" id="file-${part.id}" accept="image/*">
                </div>
                <div class="part-preview" id="preview-${part.id}">
                    <img class="part-preview-image" id="img-${part.id}" alt="${part.name}预览">
                    <div class="part-file-info" id="info-${part.id}"></div>
                    <button type="button" class="part-remove-btn" id="remove-${part.id}">移除</button>
                </div>
            `;

            // 添加事件监听器
            setupPartEventListeners(part.id);

            return section;
        }

        // 设置部位事件监听器
        function setupPartEventListeners(partId) {
            const uploadArea = document.getElementById(`upload-${partId}`);
            const fileInput = document.getElementById(`file-${partId}`);
            const removeBtn = document.getElementById(`remove-${partId}`);

            // 点击上传区域
            uploadArea.addEventListener('click', () => {
                fileInput.click();
            });

            // 键盘支持
            uploadArea.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    fileInput.click();
                }
            });

            // 文件选择
            fileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    handleFileUpload(partId, e.target.files[0]);
                }
            });

            // 拖拽支持
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', (e) => {
                if (!uploadArea.contains(e.relatedTarget)) {
                    uploadArea.classList.remove('dragover');
                }
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFileUpload(partId, files[0]);
                }
            });

            // 移除按钮
            removeBtn.addEventListener('click', () => {
                removeFile(partId);
            });
        }

        // 表单验证函数
        function validateForm() {
            let isValid = true;

            // 验证小组编号
            if (!elements.groupNum.value) {
                showFieldError('groupNum', '请选择小组编号');
                isValid = false;
            } else {
                hideFieldError('groupNum');
            }

            // 验证所有部位是否都已上传图片
            if (uploadedFiles.size < carParts.length) {
                elements.uploadError.style.display = 'block';
                elements.uploadError.textContent = `请为所有车辆部位上传检测图片（已上传 ${uploadedFiles.size}/${carParts.length}）`;
                isValid = false;
            } else {
                elements.uploadError.style.display = 'none';
            }

            return isValid;
        }

        // 文件验证函数
        function validateFile(file) {
            const maxSize = 16 * 1024 * 1024; // 16MB
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];

            if (!allowedTypes.includes(file.type)) {
                showFieldError('fileInput', '请选择JPG、PNG或GIF格式的图片文件');
                return false;
            }

            if (file.size > maxSize) {
                showFieldError('fileInput', '文件大小不能超过16MB');
                return false;
            }

            return true;
        }

        // 处理文件上传
        function handleFileUpload(partId, file) {
            if (!validateFile(file)) {
                return;
            }

            // 存储文件
            uploadedFiles.set(partId, file);

            // 显示预览
            showFilePreview(partId, file);

            // 更新状态
            updatePartStatus(partId, 'completed');

            // 更新进度
            updateProgress();

            console.log(`${partId} 文件上传成功:`, file.name);
        }

        // 显示文件预览
        function showFilePreview(partId, file) {
            const uploadArea = document.getElementById(`upload-${partId}`);
            const preview = document.getElementById(`preview-${partId}`);
            const img = document.getElementById(`img-${partId}`);
            const info = document.getElementById(`info-${partId}`);

            const reader = new FileReader();
            reader.onload = (e) => {
                img.src = e.target.result;
                info.textContent = `${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`;

                uploadArea.classList.add('completed');
                preview.classList.add('show');
            };
            reader.readAsDataURL(file);
        }

        // 移除文件
        function removeFile(partId) {
            uploadedFiles.delete(partId);

            const uploadArea = document.getElementById(`upload-${partId}`);
            const preview = document.getElementById(`preview-${partId}`);
            const img = document.getElementById(`img-${partId}`);
            const info = document.getElementById(`info-${partId}`);

            uploadArea.classList.remove('completed');
            preview.classList.remove('show');
            img.src = '';
            info.textContent = '';

            updatePartStatus(partId, 'pending');
            updateProgress();

            console.log(`${partId} 文件已移除`);
        }

        // 更新部位状态
        function updatePartStatus(partId, status) {
            const statusElement = document.getElementById(`status-${partId}`);
            const section = document.getElementById(`section-${partId}`);

            statusElement.className = `part-status ${status}`;

            if (status === 'completed') {
                statusElement.textContent = '已上传';
                section.classList.add('completed');
                section.classList.remove('error');
            } else {
                statusElement.textContent = '待上传';
                section.classList.remove('completed');
            }
        }

        // 更新上传进度
        function updateProgress() {
            const uploaded = uploadedFiles.size;
            const total = carParts.length;
            const percentage = (uploaded / total) * 100;

            elements.uploadProgress.textContent = `${uploaded}/${total}`;
            elements.progressFill.style.width = `${percentage}%`;

            // 更新按钮状态
            if (uploaded === total) {
                elements.submitBtn.disabled = false;
                elements.submitBtn.textContent = '🔍 开始检测';
            } else {
                elements.submitBtn.disabled = true;
                elements.submitBtn.textContent = `🔍 请先上传所有图片 (${uploaded}/${total})`;
            }
        }

        // 显示字段错误
        function showFieldError(fieldName, message) {
            const field = elements[fieldName] || document.getElementById(fieldName);
            const formGroup = field.closest('.form-group');
            const errorElement = formGroup.querySelector('.error-message');

            formGroup.classList.add('error');
            if (errorElement) {
                errorElement.textContent = message;
            }

            // 设置焦点到错误字段
            field.focus();
        }

        // 隐藏字段错误
        function hideFieldError(fieldName) {
            const field = elements[fieldName] || document.getElementById(fieldName);
            const formGroup = field.closest('.form-group');

            formGroup.classList.remove('error');
        }
        
        // 实时表单验证
        elements.groupNum.addEventListener('change', () => hideFieldError('groupNum'));
        

        
        // 加载步骤动画
        function showLoadingSteps() {
            const steps = ['step1', 'step2', 'step3', 'step4', 'step5'];
            let currentStep = 0;

            const stepInterval = setInterval(() => {
                // 移除当前步骤的active状态
                if (currentStep > 0) {
                    document.getElementById(steps[currentStep - 1]).classList.remove('active');
                }

                // 添加下一步骤的active状态
                if (currentStep < steps.length) {
                    document.getElementById(steps[currentStep]).classList.add('active');
                    currentStep++;
                } else {
                    clearInterval(stepInterval);
                }
            }, 800); // 每800ms切换一个步骤

            return stepInterval;
        }

        // 重置加载步骤
        function resetLoadingSteps() {
            const steps = ['step1', 'step2', 'step3', 'step4', 'step5'];
            steps.forEach((stepId, index) => {
                const step = document.getElementById(stepId);
                if (index === 0) {
                    step.classList.add('active');
                } else {
                    step.classList.remove('active');
                }
            });
        }

        // 生成模拟检测数据
        function generateMockData(groupNum) {
            const carModels = ['比亚迪海豹', '特斯拉Model 3', '蔚来ET5', '小鹏P7', '理想L7', '极氪001'];

            // 基于上传的部位生成检测数据
            const detectionData = carParts.map(part => {
                const baseValue = Math.floor(Math.random() * 40) + 80; // 80-120
                const standardMin = 90;
                const standardMax = 110;
                const deviation = baseValue < standardMin ? `偏低${standardMin - baseValue}μm` :
                                baseValue > standardMax ? `偏高${baseValue - standardMax}μm` : '标准范围内';

                const clarityScore = Math.floor(Math.random() * 20) + 80; // 80-100
                const positionScore = Math.floor(Math.random() * 20) + 80; // 80-100
                const totalScore = Math.floor((clarityScore + positionScore) / 2);

                let grade = '一般';
                if (totalScore >= 95) grade = '优秀';
                else if (totalScore >= 85) grade = '良好';
                else if (totalScore >= 75) grade = '一般';
                else grade = '待改进';

                return {
                    part: part.name,
                    standard_range: '90-110',
                    actual_value: baseValue,
                    deviation: deviation,
                    clarity_score: clarityScore,
                    position_score: positionScore,
                    total_score: totalScore,
                    grade: grade,
                    uploaded_file: uploadedFiles.get(part.id)?.name || '未知文件'
                };
            });

            // 计算综合数据
            const avgClarity = Math.floor(detectionData.reduce((sum, item) => sum + item.clarity_score, 0) / detectionData.length);
            const avgPosition = Math.floor(detectionData.reduce((sum, item) => sum + item.position_score, 0) / detectionData.length);
            const avgAccuracy = Math.floor(Math.random() * 20) + 80;
            const overallScore = Math.floor((avgAccuracy * 0.4 + avgClarity * 0.3 + avgPosition * 0.3));

            let overallGrade = '合格';
            if (overallScore >= 95) overallGrade = '优秀';
            else if (overallScore >= 85) overallGrade = '良好';
            else if (overallScore >= 75) overallGrade = '合格';
            else overallGrade = '待改进';

            const excellentCount = detectionData.filter(item => item.grade === '优秀').length;
            const goodCount = detectionData.filter(item => item.grade === '良好').length;
            const avgThickness = Math.floor(detectionData.reduce((sum, item) => sum + item.actual_value, 0) / detectionData.length);

            return {
                status: 'success',
                group_num: groupNum,
                car_model: carModels[Math.floor(Math.random() * carModels.length)],
                detection_time: new Date().toLocaleString('zh-CN'),
                overall_score: overallScore,
                overall_grade: overallGrade,
                detection_data: detectionData,
                total_accuracy: avgAccuracy,
                total_clarity: avgClarity,
                total_position: avgPosition,
                excellent_count: excellentCount,
                good_count: goodCount,
                avg_thickness: avgThickness,
                uploaded_parts_count: uploadedFiles.size,
                conclusion: `本次检测共涉及${detectionData.length}个关键部位，其中${excellentCount}个部位达到优秀标准，${goodCount}个部位达到良好标准。整体漆膜厚度分布较为均匀，符合新能源汽车制造标准。`,
                improvement: excellentCount < 3 ? '建议加强测量点位的精确定位，提高数值读取的清晰度，确保测量环境光线充足。' : '检测质量优秀，建议保持当前的操作标准和测量精度。'
            };
        }

        // 表单提交处理（多部位版本）
        elements.uploadForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            // 表单验证
            if (!validateForm()) {
                console.log('表单验证失败');
                return;
            }

            try {
                // 获取表单数据
                const groupNum = elements.groupNum.value;

                console.log('开始检测处理:', {
                    groupNum,
                    uploadedParts: Array.from(uploadedFiles.keys()),
                    totalFiles: uploadedFiles.size
                });

                // 重置并显示加载状态
                resetLoadingSteps();
                elements.loading.style.display = 'block';
                elements.result.style.display = 'none';
                elements.submitBtn.disabled = true;
                elements.submitBtn.textContent = '🔍 检测中...';

                // 开始步骤动画
                const stepInterval = showLoadingSteps();

                // 模拟处理时间（根据上传文件数量调整）
                const processingTime = 3000 + (uploadedFiles.size * 500); // 基础3秒 + 每个文件500ms

                setTimeout(() => {
                    try {
                        clearInterval(stepInterval);

                        // 生成模拟数据
                        const mockData = generateMockData(groupNum);

                        // 隐藏加载状态
                        elements.loading.style.display = 'none';
                        elements.submitBtn.disabled = false;
                        elements.submitBtn.textContent = '🔍 开始检测';

                        // 显示结果
                        showResult(mockData);
                    } catch (error) {
                        console.error('数据生成错误:', error);
                        showError('数据处理过程中发生错误，请重试');
                    }
                }, processingTime);

            } catch (error) {
                console.error('表单提交错误:', error);
                showError('提交过程中发生错误，请检查网络连接后重试');

                // 恢复按钮状态
                elements.loading.style.display = 'none';
                elements.submitBtn.disabled = false;
                elements.submitBtn.textContent = '🔍 开始检测';
            }
        });

        // 错误显示函数
        function showError(message) {
            elements.result.style.display = 'block';
            elements.result.className = 'result error';
            document.getElementById('resultTitle').textContent = '检测失败';
            document.getElementById('resultContent').innerHTML = `
                <div class="result-item">
                    <span class="result-label">错误信息：</span>
                    <span class="result-value">${message}</span>
                </div>
                <div style="margin-top: 15px;">
                    <button type="button" class="btn" onclick="location.reload()">
                        🔄 重新开始
                    </button>
                </div>
            `;
            elements.generateReportBtn.style.display = 'none';
        }
        
        // 显示检测结果
        function showResult(data) {
            currentResult = data;
            result.style.display = 'block';

            if (data.status === 'error') {
                result.className = 'result error';
                document.getElementById('resultTitle').textContent = '检测失败';
                document.getElementById('resultContent').innerHTML = `
                    <div class="result-item">
                        <span class="result-label">错误信息：</span>
                        <span class="result-value">${data.message}</span>
                    </div>
                `;
                generateReportBtn.style.display = 'none';
                return;
            }

            // 根据综合评分设置样式
            let statusClass = 'success';
            if (data.overall_score >= 94) {
                statusClass = 'success';
            } else if (data.overall_score >= 85) {
                statusClass = 'warning';
            } else {
                statusClass = 'error';
            }

            result.className = `result ${statusClass}`;
            document.getElementById('resultTitle').textContent = `${data.group_num}车漆侧厚评估报告`;

            let contentHTML = `
                <style>
                    .report-table {
                        width: 100%;
                        border-collapse: collapse;
                        margin: 15px 0;
                        font-size: 0.9em;
                    }
                    .report-table th,
                    .report-table td {
                        border: 1px solid #ddd;
                        padding: 8px;
                        text-align: center;
                    }
                    .report-table th {
                        background-color: #4facfe;
                        color: white;
                        font-weight: 600;
                    }
                    .report-table tr:nth-child(even) {
                        background-color: #f9f9f9;
                    }
                    .report-table tr:hover {
                        background-color: #f5f5f5;
                    }
                    .grade-excellent { color: #28a745; font-weight: 600; }
                    .grade-good { color: #17a2b8; font-weight: 600; }
                    .grade-normal { color: #ffc107; font-weight: 600; }
                    .grade-poor { color: #dc3545; font-weight: 600; }
                    .section-title {
                        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                        color: white;
                        padding: 10px 15px;
                        margin: 20px 0 10px 0;
                        border-radius: 6px;
                        font-weight: 600;
                    }
                    .info-grid {
                        display: grid;
                        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                        gap: 10px;
                        margin: 15px 0;
                    }
                    .info-card {
                        background: #f8f9ff;
                        padding: 12px;
                        border-radius: 6px;
                        border-left: 4px solid #4facfe;
                    }
                    .info-label {
                        font-size: 0.8em;
                        color: #666;
                        margin-bottom: 5px;
                    }
                    .info-value {
                        font-size: 1.1em;
                        font-weight: 600;
                        color: #333;
                    }
                </style>

                <div class="section-title">📋 一、检测基本信息</div>
                <div class="info-grid">
                    <div class="info-card">
                        <div class="info-label">小组编号</div>
                        <div class="info-value">${data.group_num}</div>
                    </div>
                    <div class="info-card">
                        <div class="info-label">检测车辆</div>
                        <div class="info-value">${data.car_model}</div>
                    </div>
                    <div class="info-card">
                        <div class="info-label">检测时间</div>
                        <div class="info-value">${data.detection_time}</div>
                    </div>
                    <div class="info-card">
                        <div class="info-label">综合评分</div>
                        <div class="info-value">${data.overall_score}分 (${data.overall_grade})</div>
                    </div>
                </div>

                <div class="section-title">📊 二、各部位测量数据与评分</div>
                <table class="report-table">
                    <thead>
                        <tr>
                            <th>检测部位</th>
                            <th>标准范围(μm)</th>
                            <th>实测值(μm)</th>
                            <th>与标准偏差</th>
                            <th>清晰度评分</th>
                            <th>位置规范性</th>
                            <th>单项得分</th>
                            <th>评级</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${data.detection_data.map(item => `
                            <tr>
                                <td>${item.part}</td>
                                <td>${item.standard_range}</td>
                                <td><strong>${item.actual_value}</strong></td>
                                <td>${item.deviation}</td>
                                <td>${item.clarity_score}分</td>
                                <td>${item.position_score}分</td>
                                <td>${item.total_score}分</td>
                                <td class="grade-${item.grade === '优秀' ? 'excellent' : item.grade === '良好' ? 'good' : 'normal'}">${item.grade}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>

                <div class="section-title">📈 三、评分维度详解</div>
                <table class="report-table">
                    <thead>
                        <tr>
                            <th>评分维度</th>
                            <th>权重</th>
                            <th>评分标准</th>
                            <th>小组得分</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>数值准确性</td>
                            <td>40%</td>
                            <td>标准范围内得100分，每超/低1%扣2分</td>
                            <td>${data.total_accuracy}分</td>
                        </tr>
                        <tr>
                            <td>清晰度</td>
                            <td>30%</td>
                            <td>漆膜仪数值OCR识别置信度>90%得100分</td>
                            <td>${data.total_clarity}分</td>
                        </tr>
                        <tr>
                            <td>位置规范性</td>
                            <td>30%</td>
                            <td>测量点位于部件中心区域得100分</td>
                            <td>${data.total_position}分</td>
                        </tr>
                        <tr style="background-color: #e3f2fd;">
                            <td><strong>合计</strong></td>
                            <td><strong>100%</strong></td>
                            <td><strong>-</strong></td>
                            <td><strong>${data.overall_score}分</strong></td>
                        </tr>
                    </tbody>
                </table>

                <div class="section-title">💡 四、检测结论与建议</div>
                <div class="info-grid">
                    <div class="info-card">
                        <div class="info-label">综合评级</div>
                        <div class="info-value">${data.overall_grade}（${data.overall_score}分，标准60分合格）</div>
                    </div>
                    <div class="info-card">
                        <div class="info-label">优秀部位</div>
                        <div class="info-value">${data.excellent_count}个</div>
                    </div>
                    <div class="info-card">
                        <div class="info-label">良好部位</div>
                        <div class="info-value">${data.good_count}个</div>
                    </div>
                    <div class="info-card">
                        <div class="info-label">平均厚度</div>
                        <div class="info-value">${data.avg_thickness}μm</div>
                    </div>
                </div>

                <div style="background: #f8f9ff; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #4facfe;">
                    <h4 style="color: #4facfe; margin-bottom: 10px;">🎯 亮点分析</h4>
                    <p style="color: #666; line-height: 1.6;">${data.conclusion}</p>
                </div>

                <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #ffc107;">
                    <h4 style="color: #856404; margin-bottom: 10px;">⚠️ 待改进点</h4>
                    <p style="color: #856404; line-height: 1.6;">${data.improvement}</p>
                </div>
            `;

            document.getElementById('resultContent').innerHTML = contentHTML;
            generateReportBtn.style.display = 'block';
        }
        
        // 生成报告（优化版本）
        elements.generateReportBtn.addEventListener('click', () => {
            if (!currentResult) {
                console.error('没有可用的检测结果数据');
                return;
            }

            elements.generateReportBtn.disabled = true;
            elements.generateReportBtn.textContent = '正在生成报告...';

            try {
                // 准备报告数据
                const reportData = {
                    report_id: `RPT-${new Date().getFullYear()}-${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}`,
                    generate_time: new Date().toLocaleString('zh-CN'),
                    group_num: currentResult.group_num,
                    car_part: '车漆检测',
                    upload_time: currentResult.detection_time,
                    result_status: currentResult.overall_score >= 85 ? 'normal' : 'warning',
                    avg_thickness: currentResult.avg_thickness,
                    max_thickness: Math.max(...currentResult.detection_data.map(d => d.actual_value)),
                    min_thickness: Math.min(...currentResult.detection_data.map(d => d.actual_value)),
                    thickness_diff: Math.max(...currentResult.detection_data.map(d => d.actual_value)) - Math.min(...currentResult.detection_data.map(d => d.actual_value)),
                    standard_range: '90-110 μm',
                    detection_count: currentResult.detection_data.length,
                    detected_values: currentResult.detection_data.map(d => d.actual_value),
                    issues: currentResult.overall_score < 85 ? ['检测精度需要提升', '部分测量点位置不够规范'] : []
                };

                // 保存数据到localStorage
                localStorage.setItem('carPaintReportData', JSON.stringify(reportData));
                console.log('报告数据已保存到localStorage:', reportData);

                // 模拟报告生成过程
                setTimeout(() => {
                    // 打开报告页面
                    const reportWindow = window.open('report.html', '_blank');

                    if (!reportWindow) {
                        alert('无法打开报告窗口，请检查浏览器弹窗设置');
                        console.error('报告窗口被阻止');
                    } else {
                        console.log('报告窗口已打开');
                    }

                    // 恢复按钮状态
                    elements.generateReportBtn.disabled = false;
                    elements.generateReportBtn.textContent = '📄 生成检测报告';
                }, 1500); // 1.5秒模拟生成时间

            } catch (error) {
                console.error('生成报告失败:', error);
                alert('生成报告失败，请重试');

                // 恢复按钮状态
                elements.generateReportBtn.disabled = false;
                elements.generateReportBtn.textContent = '📄 生成检测报告';
            }
        });

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面DOM加载完成，开始初始化');

            // 初始化上传区域
            initializeUploadSections();

            // 初始化按钮状态
            elements.submitBtn.disabled = true;
            elements.submitBtn.textContent = `🔍 请先上传所有图片 (0/${carParts.length})`;

            console.log('页面初始化完成');
        });
    </script>
</body>
</html>
