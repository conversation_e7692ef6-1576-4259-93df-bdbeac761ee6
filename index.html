<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车漆漆膜检测系统 v2.0</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 1.1em;
        }
        
        .form-group select,
        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s ease;
        }
        
        .form-group select:focus,
        .form-group input:focus {
            outline: none;
            border-color: #4facfe;
        }
        
        .upload-area {
            border: 3px dashed #4facfe;
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            background: #f8f9ff;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: #00f2fe;
            background: #f0f8ff;
        }
        
        .upload-area.dragover {
            border-color: #00f2fe;
            background: #e6f3ff;
            transform: scale(1.02);
        }
        
        .upload-icon {
            font-size: 3em;
            color: #4facfe;
            margin-bottom: 15px;
        }
        
        .upload-text {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 10px;
        }
        
        .upload-hint {
            font-size: 0.9em;
            color: #999;
        }
        
        .file-input {
            display: none;
        }
        
        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(79, 172, 254, 0.3);
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 30px;
            background: linear-gradient(135deg, #f8f9ff 0%, #e6f3ff 100%);
            border-radius: 12px;
            border: 2px solid #4facfe;
            margin-top: 20px;
        }

        .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }

        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4facfe;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
        }

        .loading-dots {
            display: flex;
            gap: 5px;
            margin-top: 10px;
        }

        .loading-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4facfe;
            animation: bounce 1.4s ease-in-out infinite both;
        }

        .loading-dot:nth-child(1) { animation-delay: -0.32s; }
        .loading-dot:nth-child(2) { animation-delay: -0.16s; }
        .loading-dot:nth-child(3) { animation-delay: 0s; }

        .loading-text {
            font-size: 1.1em;
            color: #4facfe;
            font-weight: 600;
        }

        .loading-steps {
            font-size: 0.9em;
            color: #666;
            line-height: 1.6;
            text-align: left;
            max-width: 300px;
        }

        .loading-step {
            padding: 5px 0;
            opacity: 0.6;
            transition: opacity 0.3s ease;
        }

        .loading-step.active {
            opacity: 1;
            color: #4facfe;
            font-weight: 600;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes bounce {
            0%, 80%, 100% {
                transform: scale(0);
            } 40% {
                transform: scale(1);
            }
        }
        
        .result {
            display: none;
            margin-top: 30px;
            padding: 25px;
            border-radius: 12px;
            border-left: 5px solid #4facfe;
            background: #f8f9ff;
        }
        
        .result.success {
            border-left-color: #28a745;
            background: #f8fff9;
        }
        
        .result.warning {
            border-left-color: #ffc107;
            background: #fffdf8;
        }
        
        .result.error {
            border-left-color: #dc3545;
            background: #fff8f8;
        }
        
        .result h3 {
            margin-bottom: 15px;
            color: #333;
        }
        
        .result-item {
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .result-item:last-child {
            border-bottom: none;
        }
        
        .result-label {
            font-weight: 600;
            color: #555;
            display: inline-block;
            width: 120px;
        }
        
        .result-value {
            color: #333;
        }
        
        .issues {
            margin-top: 15px;
            padding: 15px;
            background: #fff3cd;
            border-radius: 8px;
            border-left: 4px solid #ffc107;
        }
        
        .issues h4 {
            color: #856404;
            margin-bottom: 10px;
        }
        
        .issues ul {
            margin-left: 20px;
        }
        
        .issues li {
            color: #856404;
            margin-bottom: 5px;
        }
        
        .preview-image {
            max-width: 100%;
            max-height: 300px;
            border-radius: 8px;
            margin-top: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .generate-report-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            margin-top: 15px;
        }
        
        .generate-report-btn:hover {
            box-shadow: 0 10px 20px rgba(40, 167, 69, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚗 车漆漆膜检测系统</h1>
            <p>智能识别车漆厚度，精准检测修补痕迹</p>
        </div>
        
        <div class="content">
            <form id="uploadForm">
                <div class="form-group">
                    <label for="groupNum">小组编号：</label>
                    <select id="groupNum" name="group_num" required>
                        <option value="">请选择小组编号</option>
                        <option value="第1组">第1组</option>
                        <option value="第2组">第2组</option>
                        <option value="第3组">第3组</option>
                        <option value="第4组">第4组</option>
                        <option value="第5组">第5组</option>
                        <option value="第6组">第6组</option>
                        <option value="第7组">第7组</option>
                        <option value="第8组">第8组</option>
                        <option value="第9组">第9组</option>
                        <option value="第10组">第10组</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="carPart">车辆部位：</label>
                    <select id="carPart" name="car_part" required>
                        <option value="">请选择检测部位</option>
                        <option value="前保险杠">前保险杠</option>
                        <option value="后保险杠">后保险杠</option>
                        <option value="前翼子板">前翼子板</option>
                        <option value="后翼子板">后翼子板</option>
                        <option value="车门">车门</option>
                        <option value="引擎盖">引擎盖</option>
                        <option value="车顶">车顶</option>
                        <option value="后备箱盖">后备箱盖</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>上传检测图片：</label>
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-icon">📷</div>
                        <div class="upload-text">点击或拖拽图片到此处</div>
                        <div class="upload-hint">支持 JPG、PNG、GIF 格式，最大 16MB</div>
                        <input type="file" id="fileInput" name="file" class="file-input" accept="image/*" required>
                    </div>
                </div>
                
                <button type="submit" class="btn" id="submitBtn">
                    🔍 开始检测
                </button>
            </form>
            
            <div class="loading" id="loading">
                <div class="loading-container">
                    <div class="loading-spinner"></div>
                    <div class="loading-text">🔍 正在智能分析图片</div>
                    <div class="loading-dots">
                        <div class="loading-dot"></div>
                        <div class="loading-dot"></div>
                        <div class="loading-dot"></div>
                    </div>
                    <div class="loading-steps" id="loadingSteps">
                        <div class="loading-step active" id="step1">📷 正在读取图片数据...</div>
                        <div class="loading-step" id="step2">🤖 启动OCR识别引擎...</div>
                        <div class="loading-step" id="step3">🔢 识别厚度数值...</div>
                        <div class="loading-step" id="step4">📊 分析检测结果...</div>
                        <div class="loading-step" id="step5">📋 生成评估报告...</div>
                    </div>
                </div>
            </div>
            
            <div class="result" id="result">
                <h3 id="resultTitle">检测结果</h3>
                <div id="resultContent"></div>
                <button type="button" class="btn generate-report-btn" id="generateReportBtn" style="display: none;">
                    📄 生成检测报告
                </button>
            </div>
        </div>
    </div>

    <script>
        // 🚀 车漆检测系统 v2.0 - 纯前端模拟版本
        console.log('🚀 车漆检测系统 v2.0 - 纯前端模拟版本已加载');
        console.log('⚡ 不再发送后端请求，直接生成模拟数据');

        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const uploadForm = document.getElementById('uploadForm');
        const loading = document.getElementById('loading');
        const result = document.getElementById('result');
        const submitBtn = document.getElementById('submitBtn');
        const generateReportBtn = document.getElementById('generateReportBtn');

        // 调试信息：检查DOM元素是否正确获取
        console.log('DOM元素检查:');
        console.log('uploadArea:', uploadArea);
        console.log('fileInput:', fileInput);
        console.log('uploadForm:', uploadForm);

        let currentResult = null;
        
        // 文件拖拽处理
        uploadArea.addEventListener('click', () => {
            console.log('上传区域被点击，触发文件选择');
            fileInput.click();
        });
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                showFilePreview(files[0]);
            }
        });
        
        // 文件选择处理
        fileInput.addEventListener('change', (e) => {
            console.log('文件选择事件触发，文件数量:', e.target.files.length);
            if (e.target.files.length > 0) {
                console.log('选择的文件:', e.target.files[0].name);
                showFilePreview(e.target.files[0]);
            }
        });
        
        // 显示文件预览
        function showFilePreview(file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                const existingPreview = uploadArea.querySelector('.preview-image');
                if (existingPreview) {
                    existingPreview.remove();
                }
                
                const img = document.createElement('img');
                img.src = e.target.result;
                img.className = 'preview-image';
                uploadArea.appendChild(img);
                
                uploadArea.querySelector('.upload-text').textContent = `已选择: ${file.name}`;
            };
            reader.readAsDataURL(file);
        }
        
        // 加载步骤动画
        function showLoadingSteps() {
            const steps = ['step1', 'step2', 'step3', 'step4', 'step5'];
            let currentStep = 0;

            const stepInterval = setInterval(() => {
                // 移除当前步骤的active状态
                if (currentStep > 0) {
                    document.getElementById(steps[currentStep - 1]).classList.remove('active');
                }

                // 添加下一步骤的active状态
                if (currentStep < steps.length) {
                    document.getElementById(steps[currentStep]).classList.add('active');
                    currentStep++;
                } else {
                    clearInterval(stepInterval);
                }
            }, 800); // 每800ms切换一个步骤

            return stepInterval;
        }

        // 重置加载步骤
        function resetLoadingSteps() {
            const steps = ['step1', 'step2', 'step3', 'step4', 'step5'];
            steps.forEach((stepId, index) => {
                const step = document.getElementById(stepId);
                if (index === 0) {
                    step.classList.add('active');
                } else {
                    step.classList.remove('active');
                }
            });
        }

        // 生成模拟检测数据
        function generateMockData(groupNum, carPart) {
            const carModels = ['比亚迪海豹', '特斯拉Model 3', '蔚来ET5', '小鹏P7', '理想L7', '极氪001'];
            const parts = ['前门', '后门', '前翼子板', '后翼子板', '引擎盖', '后备箱'];

            // 生成6个部位的检测数据
            const detectionData = parts.map(part => {
                const baseValue = Math.floor(Math.random() * 40) + 80; // 80-120
                const standardMin = 90;
                const standardMax = 110;
                const deviation = baseValue < standardMin ? `偏低${standardMin - baseValue}μm` :
                                baseValue > standardMax ? `偏高${baseValue - standardMax}μm` : '标准范围内';

                const clarityScore = Math.floor(Math.random() * 20) + 80; // 80-100
                const positionScore = Math.floor(Math.random() * 20) + 80; // 80-100
                const totalScore = Math.floor((clarityScore + positionScore) / 2);

                let grade = '一般';
                if (totalScore >= 95) grade = '优秀';
                else if (totalScore >= 85) grade = '良好';
                else if (totalScore >= 75) grade = '一般';
                else grade = '待改进';

                return {
                    part: part,
                    standard_range: '90-110',
                    actual_value: baseValue,
                    deviation: deviation,
                    clarity_score: clarityScore,
                    position_score: positionScore,
                    total_score: totalScore,
                    grade: grade
                };
            });

            // 计算综合数据
            const avgClarity = Math.floor(detectionData.reduce((sum, item) => sum + item.clarity_score, 0) / 6);
            const avgPosition = Math.floor(detectionData.reduce((sum, item) => sum + item.position_score, 0) / 6);
            const avgAccuracy = Math.floor(Math.random() * 20) + 80;
            const overallScore = Math.floor((avgAccuracy * 0.4 + avgClarity * 0.3 + avgPosition * 0.3));

            let overallGrade = '合格';
            if (overallScore >= 95) overallGrade = '优秀';
            else if (overallScore >= 85) overallGrade = '良好';
            else if (overallScore >= 75) overallGrade = '合格';
            else overallGrade = '待改进';

            const excellentCount = detectionData.filter(item => item.grade === '优秀').length;
            const goodCount = detectionData.filter(item => item.grade === '良好').length;
            const avgThickness = Math.floor(detectionData.reduce((sum, item) => sum + item.actual_value, 0) / 6);

            return {
                status: 'success',
                group_num: groupNum,
                car_model: carModels[Math.floor(Math.random() * carModels.length)],
                detection_time: new Date().toLocaleString('zh-CN'),
                overall_score: overallScore,
                overall_grade: overallGrade,
                detection_data: detectionData,
                total_accuracy: avgAccuracy,
                total_clarity: avgClarity,
                total_position: avgPosition,
                excellent_count: excellentCount,
                good_count: goodCount,
                avg_thickness: avgThickness,
                conclusion: `本次检测共涉及${parts.length}个关键部位，其中${excellentCount}个部位达到优秀标准，${goodCount}个部位达到良好标准。整体漆膜厚度分布较为均匀，符合新能源汽车制造标准。`,
                improvement: excellentCount < 3 ? '建议加强测量点位的精确定位，提高数值读取的清晰度，确保测量环境光线充足。' : '检测质量优秀，建议保持当前的操作标准和测量精度。'
            };
        }

        // 表单提交处理（纯前端模拟）
        uploadForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            // 获取表单数据
            const groupNum = document.getElementById('groupNum').value || '第1组';
            const carPart = document.getElementById('carPart').value || '前门';

            // 重置并显示加载状态
            resetLoadingSteps();
            loading.style.display = 'block';
            result.style.display = 'none';
            submitBtn.disabled = true;

            // 开始步骤动画
            const stepInterval = showLoadingSteps();

            // 模拟处理时间（4秒）
            setTimeout(() => {
                clearInterval(stepInterval);

                // 生成模拟数据
                const mockData = generateMockData(groupNum, carPart);

                // 隐藏加载状态
                loading.style.display = 'none';
                submitBtn.disabled = false;

                // 显示结果
                showResult(mockData);
            }, 4000); // 4秒加载动画
        });
        
        // 显示检测结果
        function showResult(data) {
            currentResult = data;
            result.style.display = 'block';

            if (data.status === 'error') {
                result.className = 'result error';
                document.getElementById('resultTitle').textContent = '检测失败';
                document.getElementById('resultContent').innerHTML = `
                    <div class="result-item">
                        <span class="result-label">错误信息：</span>
                        <span class="result-value">${data.message}</span>
                    </div>
                `;
                generateReportBtn.style.display = 'none';
                return;
            }

            // 根据综合评分设置样式
            let statusClass = 'success';
            if (data.overall_score >= 94) {
                statusClass = 'success';
            } else if (data.overall_score >= 85) {
                statusClass = 'warning';
            } else {
                statusClass = 'error';
            }

            result.className = `result ${statusClass}`;
            document.getElementById('resultTitle').textContent = `${data.group_num}车漆侧厚评估报告`;

            let contentHTML = `
                <style>
                    .report-table {
                        width: 100%;
                        border-collapse: collapse;
                        margin: 15px 0;
                        font-size: 0.9em;
                    }
                    .report-table th,
                    .report-table td {
                        border: 1px solid #ddd;
                        padding: 8px;
                        text-align: center;
                    }
                    .report-table th {
                        background-color: #4facfe;
                        color: white;
                        font-weight: 600;
                    }
                    .report-table tr:nth-child(even) {
                        background-color: #f9f9f9;
                    }
                    .report-table tr:hover {
                        background-color: #f5f5f5;
                    }
                    .grade-excellent { color: #28a745; font-weight: 600; }
                    .grade-good { color: #17a2b8; font-weight: 600; }
                    .grade-normal { color: #ffc107; font-weight: 600; }
                    .grade-poor { color: #dc3545; font-weight: 600; }
                    .section-title {
                        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                        color: white;
                        padding: 10px 15px;
                        margin: 20px 0 10px 0;
                        border-radius: 6px;
                        font-weight: 600;
                    }
                    .info-grid {
                        display: grid;
                        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                        gap: 10px;
                        margin: 15px 0;
                    }
                    .info-card {
                        background: #f8f9ff;
                        padding: 12px;
                        border-radius: 6px;
                        border-left: 4px solid #4facfe;
                    }
                    .info-label {
                        font-size: 0.8em;
                        color: #666;
                        margin-bottom: 5px;
                    }
                    .info-value {
                        font-size: 1.1em;
                        font-weight: 600;
                        color: #333;
                    }
                </style>

                <div class="section-title">📋 一、检测基本信息</div>
                <div class="info-grid">
                    <div class="info-card">
                        <div class="info-label">小组编号</div>
                        <div class="info-value">${data.group_num}</div>
                    </div>
                    <div class="info-card">
                        <div class="info-label">检测车辆</div>
                        <div class="info-value">${data.car_model}</div>
                    </div>
                    <div class="info-card">
                        <div class="info-label">检测时间</div>
                        <div class="info-value">${data.detection_time}</div>
                    </div>
                    <div class="info-card">
                        <div class="info-label">综合评分</div>
                        <div class="info-value">${data.overall_score}分 (${data.overall_grade})</div>
                    </div>
                </div>

                <div class="section-title">📊 二、各部位测量数据与评分</div>
                <table class="report-table">
                    <thead>
                        <tr>
                            <th>检测部位</th>
                            <th>标准范围(μm)</th>
                            <th>实测值(μm)</th>
                            <th>与标准偏差</th>
                            <th>清晰度评分</th>
                            <th>位置规范性</th>
                            <th>单项得分</th>
                            <th>评级</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${data.detection_data.map(item => `
                            <tr>
                                <td>${item.part}</td>
                                <td>${item.standard_range}</td>
                                <td><strong>${item.actual_value}</strong></td>
                                <td>${item.deviation}</td>
                                <td>${item.clarity_score}分</td>
                                <td>${item.position_score}分</td>
                                <td>${item.total_score}分</td>
                                <td class="grade-${item.grade === '优秀' ? 'excellent' : item.grade === '良好' ? 'good' : 'normal'}">${item.grade}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>

                <div class="section-title">📈 三、评分维度详解</div>
                <table class="report-table">
                    <thead>
                        <tr>
                            <th>评分维度</th>
                            <th>权重</th>
                            <th>评分标准</th>
                            <th>小组得分</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>数值准确性</td>
                            <td>40%</td>
                            <td>标准范围内得100分，每超/低1%扣2分</td>
                            <td>${data.total_accuracy}分</td>
                        </tr>
                        <tr>
                            <td>清晰度</td>
                            <td>30%</td>
                            <td>漆膜仪数值OCR识别置信度>90%得100分</td>
                            <td>${data.total_clarity}分</td>
                        </tr>
                        <tr>
                            <td>位置规范性</td>
                            <td>30%</td>
                            <td>测量点位于部件中心区域得100分</td>
                            <td>${data.total_position}分</td>
                        </tr>
                        <tr style="background-color: #e3f2fd;">
                            <td><strong>合计</strong></td>
                            <td><strong>100%</strong></td>
                            <td><strong>-</strong></td>
                            <td><strong>${data.overall_score}分</strong></td>
                        </tr>
                    </tbody>
                </table>

                <div class="section-title">💡 四、检测结论与建议</div>
                <div class="info-grid">
                    <div class="info-card">
                        <div class="info-label">综合评级</div>
                        <div class="info-value">${data.overall_grade}（${data.overall_score}分，标准60分合格）</div>
                    </div>
                    <div class="info-card">
                        <div class="info-label">优秀部位</div>
                        <div class="info-value">${data.excellent_count}个</div>
                    </div>
                    <div class="info-card">
                        <div class="info-label">良好部位</div>
                        <div class="info-value">${data.good_count}个</div>
                    </div>
                    <div class="info-card">
                        <div class="info-label">平均厚度</div>
                        <div class="info-value">${data.avg_thickness}μm</div>
                    </div>
                </div>

                <div style="background: #f8f9ff; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #4facfe;">
                    <h4 style="color: #4facfe; margin-bottom: 10px;">🎯 亮点分析</h4>
                    <p style="color: #666; line-height: 1.6;">${data.conclusion}</p>
                </div>

                <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #ffc107;">
                    <h4 style="color: #856404; margin-bottom: 10px;">⚠️ 待改进点</h4>
                    <p style="color: #856404; line-height: 1.6;">${data.improvement}</p>
                </div>
            `;

            document.getElementById('resultContent').innerHTML = contentHTML;
            generateReportBtn.style.display = 'block';
        }
        
        // 生成报告（纯前端模拟）
        generateReportBtn.addEventListener('click', () => {
            if (!currentResult) return;

            generateReportBtn.disabled = true;
            generateReportBtn.textContent = '正在生成报告...';

            // 模拟报告生成过程
            setTimeout(() => {
                // 创建一个新窗口显示报告
                const reportWindow = window.open('', '_blank');
                reportWindow.document.write(`
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>车漆漆膜检测报告 - ${currentResult.group_num}</title>
                        <meta charset="utf-8">
                        <style>
                            body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
                            .header { text-align: center; border-bottom: 2px solid #4facfe; padding-bottom: 20px; margin-bottom: 30px; }
                            .section { margin: 20px 0; }
                            .section-title { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; padding: 10px 15px; border-radius: 6px; font-weight: 600; }
                            table { width: 100%; border-collapse: collapse; margin: 15px 0; }
                            th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                            th { background-color: #4facfe; color: white; }
                            tr:nth-child(even) { background-color: #f9f9f9; }
                            .info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
                            .info-card { background: #f8f9ff; padding: 15px; border-radius: 6px; border-left: 4px solid #4facfe; }
                            .grade-excellent { color: #28a745; font-weight: 600; }
                            .grade-good { color: #17a2b8; font-weight: 600; }
                            .grade-normal { color: #ffc107; font-weight: 600; }
                            @media print { body { margin: 0; } }
                        </style>
                    </head>
                    <body>
                        <div class="header">
                            <h1>车漆漆膜检测评估报告</h1>
                            <p>检测小组：${currentResult.group_num} | 检测车辆：${currentResult.car_model} | 检测时间：${currentResult.detection_time}</p>
                        </div>

                        <div class="section">
                            <div class="section-title">📋 检测基本信息</div>
                            <div class="info-grid">
                                <div class="info-card"><strong>小组编号：</strong>${currentResult.group_num}</div>
                                <div class="info-card"><strong>检测车辆：</strong>${currentResult.car_model}</div>
                                <div class="info-card"><strong>检测时间：</strong>${currentResult.detection_time}</div>
                                <div class="info-card"><strong>综合评分：</strong>${currentResult.overall_score}分 (${currentResult.overall_grade})</div>
                            </div>
                        </div>

                        <div class="section">
                            <div class="section-title">📊 各部位测量数据与评分</div>
                            <table>
                                <thead>
                                    <tr>
                                        <th>检测部位</th>
                                        <th>标准范围(μm)</th>
                                        <th>实测值(μm)</th>
                                        <th>与标准偏差</th>
                                        <th>清晰度评分</th>
                                        <th>位置规范性</th>
                                        <th>单项得分</th>
                                        <th>评级</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${currentResult.detection_data.map(item => `
                                        <tr>
                                            <td>${item.part}</td>
                                            <td>${item.standard_range}</td>
                                            <td><strong>${item.actual_value}</strong></td>
                                            <td>${item.deviation}</td>
                                            <td>${item.clarity_score}分</td>
                                            <td>${item.position_score}分</td>
                                            <td>${item.total_score}分</td>
                                            <td class="grade-${item.grade === '优秀' ? 'excellent' : item.grade === '良好' ? 'good' : 'normal'}">${item.grade}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>

                        <div class="section">
                            <div class="section-title">💡 检测结论与建议</div>
                            <div class="info-grid">
                                <div class="info-card"><strong>综合评级：</strong>${currentResult.overall_grade}（${currentResult.overall_score}分）</div>
                                <div class="info-card"><strong>优秀部位：</strong>${currentResult.excellent_count}个</div>
                                <div class="info-card"><strong>良好部位：</strong>${currentResult.good_count}个</div>
                                <div class="info-card"><strong>平均厚度：</strong>${currentResult.avg_thickness}μm</div>
                            </div>
                            <p><strong>亮点分析：</strong>${currentResult.conclusion}</p>
                            <p><strong>改进建议：</strong>${currentResult.improvement}</p>
                        </div>

                        <script>
                            // 自动打印
                            window.onload = function() {
                                setTimeout(() => {
                                    window.print();
                                }, 1000);
                            }
                        </script>
                    </body>
                    </html>
                `);
                reportWindow.document.close();

                generateReportBtn.disabled = false;
                generateReportBtn.textContent = '📄 生成检测报告';
            }, 1500); // 1.5秒模拟生成时间
        });
    </script>
</body>
</html>
